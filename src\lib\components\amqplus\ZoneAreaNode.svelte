<script>
	import { <PERSON><PERSON>, Position } from '@xyflow/svelte';
	import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
	import { createEventDispatcher } from 'svelte';

	let { data } = $props();
	const dispatch = createEventDispatcher();

	// Get the color with opacity for background
	const getBackgroundColor = (color) => {
		// Convert hex to rgba with low opacity
		const hex = color.replace('#', '');
		const r = parseInt(hex.substr(0, 2), 16);
		const g = parseInt(hex.substr(2, 2), 16);
		const b = parseInt(hex.substr(4, 2), 16);
		return `rgba(${r}, ${g}, ${b}, 0.05)`;
	};

	// Get the color with higher opacity for border
	const getBorderColor = (color) => {
		// Convert hex to rgba with higher opacity
		const hex = color.replace('#', '');
		const r = parseInt(hex.substr(0, 2), 16);
		const g = parseInt(hex.substr(2, 2), 16);
		const b = parseInt(hex.substr(4, 2), 16);
		return `rgba(${r}, ${g}, ${b}, 0.8)`;
	};

	const backgroundColor = $derived(getBackgroundColor(data.color));
	const borderColor = $derived(getBorderColor(data.color));

	// Generate summary based on connected settings
	const summary = $derived(generateSummary(data.connectedSettings || {}));

	function generateSummary(settings) {
		if (!settings || Object.keys(settings).length === 0) {
			return 'No settings connected';
		}

		const summaryItems = [];

		switch (data.zone) {
			case 'mode':
				if (settings.scoring) summaryItems.push(`Scoring: ${settings.scoring}`);
				if (settings.answering) summaryItems.push(`Answering: ${settings.answering}`);
				break;
			case 'general':
				if (settings.numberOfPlayers) summaryItems.push(`${settings.numberOfPlayers} players`);
				if (settings.numberOfSongs) summaryItems.push(`${settings.numberOfSongs} songs`);
				if (settings.teamSize) summaryItems.push(`Team size: ${settings.teamSize}`);
				break;
			case 'quiz':
				if (settings.guessTime) summaryItems.push(`${settings.guessTime}s guess time`);
				if (settings.playbackSpeed) summaryItems.push(`${settings.playbackSpeed}x speed`);
				if (settings.extraGuessTime) summaryItems.push(`+${settings.extraGuessTime}s extra`);
				break;
			case 'anime':
				if (settings.playerScore)
					summaryItems.push(`Player: ${settings.playerScore.min}-${settings.playerScore.max}`);
				if (settings.animeScore)
					summaryItems.push(`Anime: ${settings.animeScore.min}-${settings.animeScore.max}`);
				if (settings.vintage)
					summaryItems.push(`${settings.vintage.from} - ${settings.vintage.to}`);
				break;
		}

		return summaryItems.length > 0 ? summaryItems.join(' • ') : 'Default settings';
	}
</script>

<div
	class="relative zone-area-node w-96"
	style="background: {backgroundColor}; border: 3px solid {borderColor}; border-radius: 16px;"
>
	<!-- Delete button for custom zones - positioned outside the card -->
	{#if data.isCustomZone && data.onDelete}
		<button
			class="absolute z-20 flex items-center justify-center w-8 h-8 text-sm font-bold text-white transition-colors bg-red-500 border-2 border-white rounded-full shadow-lg -top-2 -right-2 hover:bg-red-600"
			onclick={(e) => {
				e.stopPropagation();
				e.preventDefault();
				data.onDelete({ detail: { zoneId: data.zone, timestamp: data.timestamp } });
			}}
			title="Delete this zone"
		>
			×
		</button>
	{/if}

	<!-- Input handles for connecting setting nodes -->
	{#if data.zone !== 'mode'}
		<Handle
			type="target"
			position={Position.Left}
			style="width: 16px; height: 16px; background: {data.color}; border: 3px solid white;"
		/>
	{/if}

	<!-- Output handle for flow to next zone -->
	{#if data.zone !== 'anime'}
		<Handle
			type="source"
			position={Position.Right}
			style="width: 16px; height: 16px; background: {data.color}; border: 3px solid white;"
		/>
	{/if}

	<!-- Multiple input handles for individual setting nodes -->
	<Handle
		type="target"
		position={Position.Top}
		id="settings-input"
		style="width: 16px; height: 16px; background: {data.color}; border: 3px solid white; top: -8px;"
	/>

	<Card class="border-0 shadow-lg bg-white/90 backdrop-blur-sm">
		<CardHeader class="pb-4">
			<CardTitle class="flex items-center gap-3 text-xl font-bold text-gray-800">
				<span class="text-2xl">{data.icon}</span>
				<div>
					<div>{data.title}</div>
					<div class="text-sm font-normal text-gray-600">{data.description}</div>
				</div>
			</CardTitle>
		</CardHeader>
		<CardContent class="pt-0">
			<div class="space-y-3">
				<!-- Summary of connected settings -->
				<div class="p-3 rounded-lg bg-gray-50">
					<div class="mb-2 text-sm font-medium text-gray-700">Current Configuration:</div>
					<div class="text-sm leading-relaxed text-gray-600">
						{summary}
					</div>
				</div>

				<!-- Connection status -->
				<div class="flex items-center gap-2 text-xs text-gray-500">
					<div class="w-2 h-2 bg-green-400 rounded-full"></div>
					<span
						>Zone active • {Object.keys(data.connectedSettings || {}).length} settings connected</span
					>
				</div>
			</div>
		</CardContent>
	</Card>
</div>

<style>
	.zone-area-node {
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
	}

	:global(.zone-area-node .svelte-flow__handle) {
		width: 16px;
		height: 16px;
		border: 3px solid white;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
	}

	:global(.zone-area-node .svelte-flow__handle.svelte-flow__handle-left) {
		left: -8px;
	}

	:global(.zone-area-node .svelte-flow__handle.svelte-flow__handle-right) {
		right: -8px;
	}

	:global(.zone-area-node .svelte-flow__handle.svelte-flow__handle-top) {
		top: -8px;
	}
</style>
