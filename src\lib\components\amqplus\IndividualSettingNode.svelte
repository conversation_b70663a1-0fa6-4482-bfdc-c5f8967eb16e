<script>
	import { <PERSON><PERSON>, Position } from '@xyflow/svelte';
	import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';

	let { data } = $props();

	// Get the color with opacity for background
	const getBackgroundColor = (color) => {
		// Convert hex to rgba with low opacity for white base
		const hex = color.replace('#', '');
		const r = parseInt(hex.substr(0, 2), 16);
		const g = parseInt(hex.substr(2, 2), 16);
		const b = parseInt(hex.substr(4, 2), 16);
		// White background with color tint
		return `rgba(255, 255, 255, 0.95)`;
	};

	// Get the color with higher opacity for border
	const getBorderColor = (color) => {
		// Convert hex to rgba with higher opacity for brighter border
		const hex = color.replace('#', '');
		const r = parseInt(hex.substr(0, 2), 16);
		const g = parseInt(hex.substr(2, 2), 16);
		const b = parseInt(hex.substr(4, 2), 16);
		return `rgba(${r}, ${g}, ${b}, 0.9)`;
	};

	// Get color tint for the card
	const getColorTint = (color) => {
		const hex = color.replace('#', '');
		const r = parseInt(hex.substr(0, 2), 16);
		const g = parseInt(hex.substr(2, 2), 16);
		const b = parseInt(hex.substr(4, 2), 16);
		return `rgba(${r}, ${g}, ${b}, 0.08)`;
	};

	const backgroundColor = $derived(getBackgroundColor(data.color));
	const borderColor = $derived(getBorderColor(data.color));
	const colorTint = $derived(getColorTint(data.color));

	// Format setting value for display
	function formatSettingValue(value) {
		if (typeof value === 'object' && value !== null) {
			if (Array.isArray(value)) {
				return value.length > 0 ? `${value.length} items` : 'None';
			}
			// Handle range objects like { min: 1, max: 10 }
			if (value.min !== undefined && value.max !== undefined) {
				return `${value.min} - ${value.max}`;
			}
			// Handle percentage objects like { start: 0, end: 100 }
			if (value.start !== undefined && value.end !== undefined) {
				return `${value.start}% - ${value.end}%`;
			}
			return JSON.stringify(value);
		}
		if (typeof value === 'boolean') {
			return value ? 'Enabled' : 'Disabled';
		}
		if (typeof value === 'number') {
			return value.toString();
		}
		return value || 'Not set';
	}
</script>

<div
	class="individual-setting-node w-48 relative"
	style="background: {backgroundColor}; border: 2px solid {borderColor}; border-radius: 8px; box-shadow: 0 1px 4px rgba(0,0,0,0.1);"
>
	<!-- Delete button for custom individual nodes -->
	{#if !data.isDefault && data.onDelete}
		<button
			class="absolute z-20 flex items-center justify-center w-6 h-6 text-xs font-bold text-white transition-colors bg-red-500 border border-white rounded-full shadow-lg -top-2 -right-2 hover:bg-red-600"
			onclick={(e) => {
				e.stopPropagation();
				e.preventDefault();
				data.onDelete({ detail: { nodeId: data.id || data.title, zone: data.zone, timestamp: data.parentZoneTimestamp } });
			}}
			title="Delete this setting"
		>
			×
		</button>
	{/if}
	<!-- Output handle to connect to zone area -->
	<Handle type="source" position={Position.Bottom} style="width: 10px; height: 10px; background: {data.color}; border: 2px solid white;" />

	<div class="p-2">
		<!-- Compact header -->
		<div class="flex items-center gap-2 mb-2" style="background: {colorTint}; padding: 6px; border-radius: 6px; margin: -2px; margin-bottom: 6px;">
			<span class="text-sm">{data.icon}</span>
			<div class="flex-1 min-w-0">
				<div class="text-sm font-semibold text-gray-800 truncate">{data.title}</div>
			</div>
		</div>

		<!-- Compact value display -->
		<div class="bg-gray-50 rounded p-1.5 mb-1.5">
			<div class="text-xs font-medium text-gray-800 truncate">
				{formatSettingValue(data.currentValue)}
			</div>
		</div>

		<!-- Compact status indicator -->
		<div class="flex items-center gap-1 text-xs {data.isDefault ? 'text-blue-600' : 'text-green-600'}">
			<div class="w-1 h-1 rounded-full {data.isDefault ? 'bg-blue-400' : 'bg-green-400'}"></div>
			<span class="truncate">{data.isDefault ? 'Default' : 'Custom'}</span>
		</div>
	</div>
</div>

<style>
	.individual-setting-node {
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
	}
	
	:global(.individual-setting-node .svelte-flow__handle) {
		width: 12px;
		height: 12px;
		border: 2px solid white;
		box-shadow: 0 1px 3px rgba(0,0,0,0.1);
	}
	
	:global(.individual-setting-node .svelte-flow__handle.svelte-flow__handle-bottom) {
		bottom: -6px;
	}
</style>
