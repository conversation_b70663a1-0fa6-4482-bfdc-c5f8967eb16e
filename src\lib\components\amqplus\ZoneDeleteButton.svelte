<script>
	let { data } = $props();
</script>

<div
	class="zone-delete-button-overlay relative"
	style="width: {data.width}px; height: {data.height}px; pointer-events: none;"
>
	<!-- Delete button for zone with individual nodes - positioned at top right -->
	{#if data.onDelete}
		<button
			class="absolute z-30 flex items-center justify-center w-8 h-8 text-sm font-bold text-white transition-colors bg-red-500 border-2 border-white rounded-full shadow-lg -top-2 -right-2 hover:bg-red-600"
			style="pointer-events: auto;"
			onclick={(e) => {
				e.stopPropagation();
				e.preventDefault();
				data.onDelete({ detail: { zoneId: data.zone, timestamp: data.timestamp } });
			}}
			title="Delete all individual nodes in this zone"
		>
			×
		</button>
	{/if}
</div>

<style>
	.zone-delete-button-overlay {
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
	}
</style>
