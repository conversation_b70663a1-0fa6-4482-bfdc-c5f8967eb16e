<script>
	import { SvelteFlow, Controls, Background, MiniMap } from '@xyflow/svelte';
	import '@xyflow/svelte/dist/style.css';
	import SettingsNode from '$lib/components/amqplus/SettingsNode.svelte';
	import ZoneAreaNode from '$lib/components/amqplus/ZoneAreaNode.svelte';
	import IndividualSettingNode from '$lib/components/amqplus/IndividualSettingNode.svelte';
	import ZoneDeleteButton from '$lib/components/amqplus/ZoneDeleteButton.svelte';
	import NodeSidebar from '$lib/components/amqplus/NodeSidebar.svelte';
	import { Button } from '$lib/components/ui/button';
	import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';

	import '$lib/styles/amqplus.css';

	// Define node types
	const nodeTypes = {
		settings: SettingsNode,
		zoneArea: ZoneAreaNode,
		individualSetting: IndividualSettingNode,
		zoneDeleteButton: ZoneDeleteButton
	};

	// Individual node dimensions (from IndividualSettingNode.svelte - w-48 = 192px)
	const NODE_WIDTH = 192;
	const NODE_HEIGHT = 100; // Approximate height of individual setting nodes
	const ZONE_SPACING = 80; // Minimum spacing between zones

	// Zone configuration for tower layout (2 columns, multiple rows) - Horizontal arrangement
	const zoneConfigs = {
		mode: {
			baseX: 50,
			baseY: 50,
			zoneAreaY: 670, // Leveled with tallest zone (general)
			nodeSpacing: { x: 250, y: 120 }, // Much more spacing to prevent overlap
			nodeStartX: 50, // Padding from left edge of highlight box
			nodeStartY: 40, // Padding from top of highlight box
			columns: 2,
			nodeCount: 2 // 2 nodes in mode zone
		},
		general: {
			baseX: 0, // Will be calculated dynamically
			baseY: 50,
			zoneAreaY: 670, // Leveled with other zones
			nodeSpacing: { x: 250, y: 120 }, // Much more spacing to prevent overlap
			nodeStartX: 50, // Padding from left edge of highlight box
			nodeStartY: 40, // Padding from top of highlight box
			columns: 2,
			nodeCount: 9 // 9 nodes in general zone
		},
		quiz: {
			baseX: 0, // Will be calculated dynamically
			baseY: 50,
			zoneAreaY: 670, // Leveled with other zones
			nodeSpacing: { x: 250, y: 120 }, // Much more spacing to prevent overlap
			nodeStartX: 50, // Padding from left edge of highlight box
			nodeStartY: 40, // Padding from top of highlight box
			columns: 2,
			nodeCount: 7 // 7 nodes in quiz zone
		},
		anime: {
			baseX: 0, // Will be calculated dynamically
			baseY: 50,
			zoneAreaY: 670, // Leveled with other zones
			nodeSpacing: { x: 250, y: 120 }, // Much more spacing to prevent overlap
			nodeStartX: 50, // Padding from left edge of highlight box
			nodeStartY: 40, // Padding from top of highlight box
			columns: 2,
			nodeCount: 6 // 6 nodes in anime zone
		}
	};

	// Function to calculate proper zone positions to prevent overlap
	const calculateZonePositions = () => {
		const zoneOrder = ['mode', 'general', 'quiz', 'anime'];
		let currentX = 50; // Starting X position

		zoneOrder.forEach(zoneName => {
			const config = zoneConfigs[zoneName];
			config.baseX = currentX;

			// Calculate this zone's width and move to next position
			const dimensions = calculateZoneDimensions(config);
			currentX += dimensions.bgWidth + ZONE_SPACING;
		});
	};

	// Function to calculate dynamic zone background dimensions
	const calculateZoneDimensions = (config, actualNodeCount = null) => {
		const nodeCount = actualNodeCount || config.nodeCount;
		const rows = Math.ceil(nodeCount / config.columns);
		const actualColumns = Math.min(nodeCount, config.columns);

		// Calculate width: padding + nodes + spacing between nodes + padding
		const bgWidth = (config.nodeStartX * 2) + // Left and right padding
			(actualColumns * NODE_WIDTH) + // Width of all nodes
			((actualColumns - 1) * (config.nodeSpacing.x - NODE_WIDTH)); // Spacing between nodes

		// Calculate height: padding + nodes + spacing between nodes + padding
		const bgHeight = (config.nodeStartY * 2) + // Top and bottom padding
			(rows * NODE_HEIGHT) + // Height of all nodes
			((rows - 1) * (config.nodeSpacing.y - NODE_HEIGHT)); // Spacing between nodes

		return { bgWidth, bgHeight };
	};

	// Function to calculate dimensions for individual nodes area only (excluding main zone container)
	const calculateIndividualNodesAreaDimensions = (config, actualNodeCount = null) => {
		const nodeCount = actualNodeCount || config.nodeCount;
		const rows = Math.ceil(nodeCount / config.columns);
		const actualColumns = Math.min(nodeCount, config.columns);

		// Calculate width: padding + nodes + spacing between nodes + padding
		const width = (config.nodeStartX * 2) + // Left and right padding
			(actualColumns * NODE_WIDTH) + // Width of all nodes
			((actualColumns - 1) * (config.nodeSpacing.x - NODE_WIDTH)); // Spacing between nodes

		// Calculate height for just the individual nodes area (not including zone container)
		const height = (config.nodeStartY * 2) + // Top and bottom padding
			(rows * NODE_HEIGHT) + // Height of all nodes
			((rows - 1) * (config.nodeSpacing.y - NODE_HEIGHT)); // Spacing between nodes

		return { width, height };
	};

	// Calculate proper zone positions on initialization
	calculateZonePositions();

	// Store original zone positions for drag calculations
	let originalZonePositions = {};

	// Initialize original zone positions
	const initializeOriginalZonePositions = () => {
		['mode', 'general', 'quiz', 'anime'].forEach(zone => {
			const config = zoneConfigs[zone];
			const dimensions = calculateZoneDimensions(config);
			originalZonePositions[zone] = {
				baseX: config.baseX,
				baseY: config.baseY,
				zoneAreaX: config.baseX + (dimensions.bgWidth - 384) / 2,
				zoneAreaY: config.baseY + config.zoneAreaY
			};
		});
	};

	// Call initialization
	initializeOriginalZonePositions();

	// Function to update zone background dimensions and positions dynamically
	const updateZoneBackgrounds = () => {
		// First, recalculate zone positions to prevent overlap
		calculateZonePositions();

		// Update original zone positions for drag calculations
		initializeOriginalZonePositions();

		nodes = nodes.map(node => {
			if (node.id.endsWith('-zone-bg')) {
				const zone = node.id.replace('-zone-bg', '');
				const config = zoneConfigs[zone];
				if (config) {
					// Count actual nodes in this zone
					const actualNodeCount = nodes.filter(n =>
						n.type === 'individualSetting' && n.data.zone === zone
					).length;

					const dimensions = calculateZoneDimensions(config, actualNodeCount);
					const zoneColors = {
						mode: 'rgba(117, 185, 223, 0.05)',
						general: 'rgba(223, 105, 117, 0.05)',
						quiz: 'rgba(117, 223, 139, 0.05)',
						anime: 'rgba(223, 185, 117, 0.05)'
					};
					const zoneBorderColors = {
						mode: 'rgba(117, 185, 223, 0.3)',
						general: 'rgba(223, 105, 117, 0.3)',
						quiz: 'rgba(117, 223, 139, 0.3)',
						anime: 'rgba(223, 185, 117, 0.3)'
					};

					return {
						...node,
						position: { x: config.baseX, y: config.baseY }, // Update position
						style: `background: ${zoneColors[zone]}; border: 2px dashed ${zoneBorderColors[zone]}; border-radius: 12px; width: ${dimensions.bgWidth}px; height: ${dimensions.bgHeight}px; pointer-events: none;`
					};
				}
			}
			// Also update zone area node positioning to stay centered
			else if (node.id.endsWith('-zone')) {
				const zone = node.id.replace('-zone', '');
				const config = zoneConfigs[zone];
				if (config) {
					// Count actual nodes in this zone
					const actualNodeCount = nodes.filter(n =>
						n.type === 'individualSetting' && n.data.zone === zone
					).length;

					const dimensions = calculateZoneDimensions(config, actualNodeCount);

					return {
						...node,
						position: {
							x: config.baseX + (dimensions.bgWidth - 384) / 2, // Center the 384px wide zone area node
							y: config.baseY + config.zoneAreaY
						}
					};
				}
			}
			// Update zone labels
			else if (node.id.endsWith('-zone-label')) {
				const zone = node.id.replace('-zone-label', '');
				const config = zoneConfigs[zone];
				if (config) {
					return {
						...node,
						position: { x: config.baseX + 10, y: config.baseY + 10 }
					};
				}
			}
			// Update zone delete buttons
			else if (node.id.endsWith('-zone-delete-btn')) {
				const zone = node.id.replace('-zone-delete-btn', '').split('-').slice(0, -1).join('-');
				const config = zoneConfigs[zone];
				if (config) {
					// Count actual nodes in this zone
					const actualNodeCount = nodes.filter(n =>
						n.type === 'individualSetting' && n.data.zone === zone
					).length;

					const individualNodesAreaDimensions = calculateIndividualNodesAreaDimensions(config, actualNodeCount);

					return {
						...node,
						position: { x: config.baseX, y: config.baseY },
						data: {
							...node.data,
							width: individualNodesAreaDimensions.width,
							height: individualNodesAreaDimensions.height
						}
					};
				}
			}
			// Update individual setting nodes
			else if (node.type === 'individualSetting') {
				const zone = node.data.zone;
				const config = zoneConfigs[zone];
				if (config) {
					// Find this node's index among nodes in the same zone
					const zoneNodes = nodes.filter(n =>
						n.type === 'individualSetting' && n.data.zone === zone
					);
					const nodeIndex = zoneNodes.findIndex(n => n.id === node.id);

					if (nodeIndex !== -1) {
						const row = Math.floor(nodeIndex / config.columns);
						const col = nodeIndex % config.columns;

						return {
							...node,
							position: {
								x: config.baseX + config.nodeStartX + (col * config.nodeSpacing.x),
								y: config.baseY + config.nodeStartY + (row * config.nodeSpacing.y)
							}
						};
					}
				}
			}
			return node;
		});
	};

	// Handle individual node deletion
	const handleIndividualNodeDelete = (event) => {
		const { nodeId, zone, timestamp } = event.detail;

		// Confirm deletion
		const confirmed = confirm(
			`Are you sure you want to delete this setting node?\n\n` +
			"This will remove the individual setting node and its connections.\n\n" +
			"This action cannot be undone."
		);

		if (confirmed) {
			// Remove the specific individual setting node
			nodes = nodes.filter(node => {
				// Remove the specific individual setting node
				if (node.type === 'individualSetting' &&
					(node.id === nodeId || node.data.title === nodeId) &&
					node.data.parentZoneTimestamp === timestamp) {
					return false;
				}
				return true;
			});

			// Remove related edges
			edges = edges.filter(edge => {
				// Remove edges that connect to nodes we just deleted
				const sourceExists = nodes.some(node => node.id === edge.source);
				const targetExists = nodes.some(node => node.id === edge.target);
				return sourceExists && targetExists;
			});

			// Update zone backgrounds
			updateZoneBackgrounds();
		}
	};

	// Handle zone deletion (delete entire zone including highlight)
	const handleZoneDelete = (event) => {
		const { zoneId, timestamp } = event.detail;

		// Confirm deletion
		const confirmed = confirm(
			`Are you sure you want to delete the entire ${zoneId} zone?\n\n` +
			"This will remove:\n" +
			"• The zone background and highlight\n" +
			"• All individual setting nodes in this zone\n" +
			"• The zone area container\n" +
			"• The zone label\n" +
			"• All connections to this zone\n\n" +
			"This action cannot be undone."
		);

		if (confirmed) {
			// Remove all nodes related to this zone instance
			nodes = nodes.filter(node => {
				// Remove individual setting nodes with matching parent zone timestamp
				if (node.type === 'individualSetting' && node.data.parentZoneTimestamp === timestamp) {
					return false;
				}
				// Remove zone area node with matching timestamp
				if (node.type === 'zoneArea' && node.data.timestamp === timestamp) {
					return false;
				}
				// Remove zone delete button with matching timestamp
				if (node.type === 'zoneDeleteButton' && node.data.timestamp === timestamp) {
					return false;
				}
				// Remove zone background and label with matching timestamp
				if (node.id.includes(`-${timestamp}`)) {
					return false;
				}
				// For default zones, also remove nodes with default IDs
				if (timestamp.startsWith('default-')) {
					const baseZoneId = timestamp.replace('default-', '');
					if (node.id === `${baseZoneId}-zone` ||
						node.id === `${baseZoneId}-zone-bg` ||
						node.id === `${baseZoneId}-zone-label` ||
						node.id === `${baseZoneId}-zone-delete-btn`) {
						return false;
					}
				}
				return true;
			});

			// Remove related edges
			edges = edges.filter(edge => {
				// Remove edges that connect to nodes we just deleted
				const sourceExists = nodes.some(node => node.id === edge.source);
				const targetExists = nodes.some(node => node.id === edge.target);
				return sourceExists && targetExists;
			});

			// Update zone backgrounds
			updateZoneBackgrounds();
		}
	};

	// Define initial nodes for the actual editor
	let initialNodes = [
		// Zone Background Areas (Visual boundaries) - Tower layout with dynamic dimensions
		{
			id: 'mode-zone-bg',
			type: 'default',
			position: { x: zoneConfigs.mode.baseX, y: zoneConfigs.mode.baseY },
			data: { label: '' },
			style: `background: rgba(117, 185, 223, 0.05); border: 2px dashed rgba(117, 185, 223, 0.3); border-radius: 12px; width: ${calculateZoneDimensions(zoneConfigs.mode).bgWidth}px; height: ${calculateZoneDimensions(zoneConfigs.mode).bgHeight}px; pointer-events: none;`,
			selectable: false,
			draggable: false,
			deletable: false
		},
		{
			id: 'general-zone-bg',
			type: 'default',
			position: { x: zoneConfigs.general.baseX, y: zoneConfigs.general.baseY },
			data: { label: '' },
			style: `background: rgba(223, 105, 117, 0.05); border: 2px dashed rgba(223, 105, 117, 0.3); border-radius: 12px; width: ${calculateZoneDimensions(zoneConfigs.general).bgWidth}px; height: ${calculateZoneDimensions(zoneConfigs.general).bgHeight}px; pointer-events: none;`,
			selectable: false,
			draggable: false,
			deletable: false
		},
		{
			id: 'quiz-zone-bg',
			type: 'default',
			position: { x: zoneConfigs.quiz.baseX, y: zoneConfigs.quiz.baseY },
			data: { label: '' },
			style: `background: rgba(117, 223, 139, 0.05); border: 2px dashed rgba(117, 223, 139, 0.3); border-radius: 12px; width: ${calculateZoneDimensions(zoneConfigs.quiz).bgWidth}px; height: ${calculateZoneDimensions(zoneConfigs.quiz).bgHeight}px; pointer-events: none;`,
			selectable: false,
			draggable: false,
			deletable: false
		},
		{
			id: 'anime-zone-bg',
			type: 'default',
			position: { x: zoneConfigs.anime.baseX, y: zoneConfigs.anime.baseY },
			data: { label: '' },
			style: `background: rgba(223, 185, 117, 0.05); border: 2px dashed rgba(223, 185, 117, 0.3); border-radius: 12px; width: ${calculateZoneDimensions(zoneConfigs.anime).bgWidth}px; height: ${calculateZoneDimensions(zoneConfigs.anime).bgHeight}px; pointer-events: none;`,
			selectable: false,
			draggable: false,
			deletable: false
		},

		// Zone Labels
		{
			id: 'mode-zone-label',
			type: 'default',
			position: { x: zoneConfigs.mode.baseX + 10, y: zoneConfigs.mode.baseY + 10 },
			data: { label: '🎮 Mode Zone' },
			style: 'background: transparent; border: none; color: rgba(117, 185, 223, 0.8); font-weight: bold; font-size: 14px; pointer-events: none;',
			selectable: false,
			draggable: false,
			deletable: false
		},
		{
			id: 'general-zone-label',
			type: 'default',
			position: { x: zoneConfigs.general.baseX + 10, y: zoneConfigs.general.baseY + 10 },
			data: { label: '⚙️ General Zone' },
			style: 'background: transparent; border: none; color: rgba(223, 105, 117, 0.8); font-weight: bold; font-size: 14px; pointer-events: none;',
			selectable: false,
			draggable: false,
			deletable: false
		},
		{
			id: 'quiz-zone-label',
			type: 'default',
			position: { x: zoneConfigs.quiz.baseX + 10, y: zoneConfigs.quiz.baseY + 10 },
			data: { label: '⏱️ Quiz Zone' },
			style: 'background: transparent; border: none; color: rgba(117, 223, 139, 0.8); font-weight: bold; font-size: 14px; pointer-events: none;',
			selectable: false,
			draggable: false,
			deletable: false
		},
		{
			id: 'anime-zone-label',
			type: 'default',
			position: { x: zoneConfigs.anime.baseX + 10, y: zoneConfigs.anime.baseY + 10 },
			data: { label: '📺 Anime Zone' },
			style: 'background: transparent; border: none; color: rgba(223, 185, 117, 0.8); font-weight: bold; font-size: 14px; pointer-events: none;',
			selectable: false,
			draggable: false,
			deletable: false
		},

		// Zone Area Nodes (Main areas that show summaries) - Positioned at bottom of zones, centered
		{
			id: 'mode-zone',
			type: 'zoneArea',
			position: {
				x: zoneConfigs.mode.baseX + (calculateZoneDimensions(zoneConfigs.mode).bgWidth - 384) / 2, // Center the 384px wide zone area node
				y: zoneConfigs.mode.baseY + zoneConfigs.mode.zoneAreaY
			},
			data: {
				title: 'Mode Settings',
				description: 'Scoring & Answering',
				icon: '🎮',
				color: '#75B9DF',
				zone: 'mode',
				connectedSettings: {
					scoring: 'count',
					answering: 'typing'
				},
				timestamp: 'default-mode'
			},
			deletable: false,
			draggable: true
		},
		{
			id: 'general-zone',
			type: 'zoneArea',
			position: {
				x: zoneConfigs.general.baseX + (calculateZoneDimensions(zoneConfigs.general).bgWidth - 384) / 2, // Center the 384px wide zone area node
				y: zoneConfigs.general.baseY + zoneConfigs.general.zoneAreaY
			},
			data: {
				title: 'General Settings',
				description: 'Players & Songs',
				icon: '⚙️',
				color: '#DF6975',
				zone: 'general',
				connectedSettings: {
					numberOfPlayers: 8,
					teamSize: 1,
					numberOfSongs: 20
				},
				timestamp: 'default-general'
			},
			deletable: false,
			draggable: true
		},
		{
			id: 'quiz-zone',
			type: 'zoneArea',
			position: {
				x: zoneConfigs.quiz.baseX + (calculateZoneDimensions(zoneConfigs.quiz).bgWidth - 384) / 2, // Center the 384px wide zone area node
				y: zoneConfigs.quiz.baseY + zoneConfigs.quiz.zoneAreaY
			},
			data: {
				title: 'Quiz Settings',
				description: 'Timing & Difficulty',
				icon: '⏱️',
				color: '#75DF8B',
				zone: 'quiz',
				connectedSettings: {
					guessTime: 20,
					extraGuessTime: 0,
					playbackSpeed: 1.0
				},
				timestamp: 'default-quiz'
			},
			deletable: false,
			draggable: true
		},
		{
			id: 'anime-zone',
			type: 'zoneArea',
			position: {
				x: zoneConfigs.anime.baseX + (calculateZoneDimensions(zoneConfigs.anime).bgWidth - 384) / 2, // Center the 384px wide zone area node
				y: zoneConfigs.anime.baseY + zoneConfigs.anime.zoneAreaY
			},
			data: {
				title: 'Anime Settings',
				description: 'Filters & Vintage',
				icon: '📺',
				color: '#DFB975',
				zone: 'anime',
				connectedSettings: {
					playerScore: { min: 1, max: 10 },
					animeScore: { min: 2, max: 10 },
					vintage: { from: 'Winter 1944', to: 'Fall 2025' }
				},
				timestamp: 'default-anime'
			},
			deletable: false,
			draggable: true
		},

		// Zone Delete Buttons - positioned over individual nodes areas
		{
			id: 'mode-zone-delete-btn',
			type: 'zoneDeleteButton',
			position: {
				x: zoneConfigs.mode.baseX,
				y: zoneConfigs.mode.baseY
			},
			data: {
				zone: 'mode',
				timestamp: 'default-mode',
				width: calculateIndividualNodesAreaDimensions(zoneConfigs.mode).width,
				height: calculateIndividualNodesAreaDimensions(zoneConfigs.mode).height,
				onDelete: handleZoneDelete
			},
			selectable: false,
			draggable: false,
			deletable: false
		},
		{
			id: 'general-zone-delete-btn',
			type: 'zoneDeleteButton',
			position: {
				x: zoneConfigs.general.baseX,
				y: zoneConfigs.general.baseY
			},
			data: {
				zone: 'general',
				timestamp: 'default-general',
				width: calculateIndividualNodesAreaDimensions(zoneConfigs.general).width,
				height: calculateIndividualNodesAreaDimensions(zoneConfigs.general).height,
				onDelete: handleZoneDelete
			},
			selectable: false,
			draggable: false,
			deletable: false
		},
		{
			id: 'quiz-zone-delete-btn',
			type: 'zoneDeleteButton',
			position: {
				x: zoneConfigs.quiz.baseX,
				y: zoneConfigs.quiz.baseY
			},
			data: {
				zone: 'quiz',
				timestamp: 'default-quiz',
				width: calculateIndividualNodesAreaDimensions(zoneConfigs.quiz).width,
				height: calculateIndividualNodesAreaDimensions(zoneConfigs.quiz).height,
				onDelete: handleZoneDelete
			},
			selectable: false,
			draggable: false,
			deletable: false
		},
		{
			id: 'anime-zone-delete-btn',
			type: 'zoneDeleteButton',
			position: {
				x: zoneConfigs.anime.baseX,
				y: zoneConfigs.anime.baseY
			},
			data: {
				zone: 'anime',
				timestamp: 'default-anime',
				width: calculateIndividualNodesAreaDimensions(zoneConfigs.anime).width,
				height: calculateIndividualNodesAreaDimensions(zoneConfigs.anime).height,
				onDelete: handleZoneDelete
			},
			selectable: false,
			draggable: false,
			deletable: false
		},

		// Individual Setting Nodes - Mode Zone (2 nodes) - 2 columns tower layout
		{
			id: 'scoring-setting',
			type: 'individualSetting',
			position: {
				x: zoneConfigs.mode.baseX + zoneConfigs.mode.nodeStartX,
				y: zoneConfigs.mode.baseY + zoneConfigs.mode.nodeStartY
			},
			data: {
				title: 'Scoring',
				icon: '🏆',
				color: '#75B9DF',
				zone: 'mode',
				currentValue: 'count',
				isDefault: true,
				parentZoneTimestamp: 'default-mode',
				onDelete: handleIndividualNodeDelete
			},
			deletable: false,
			draggable: false
		},
		{
			id: 'answering-setting',
			type: 'individualSetting',
			position: {
				x: zoneConfigs.mode.baseX + zoneConfigs.mode.nodeStartX + zoneConfigs.mode.nodeSpacing.x,
				y: zoneConfigs.mode.baseY + zoneConfigs.mode.nodeStartY
			},
			data: {
				title: 'Answering',
				icon: '⌨️',
				color: '#75B9DF',
				zone: 'mode',
				currentValue: 'typing',
				isDefault: true,
				parentZoneTimestamp: 'default-mode',
				onDelete: handleIndividualNodeDelete
			},
			deletable: false,
			draggable: false
		},

		// Individual Setting Nodes - General Zone (9 nodes) - 2 columns tower layout
		{
			id: 'players-setting',
			type: 'individualSetting',
			position: {
				x: zoneConfigs.general.baseX + zoneConfigs.general.nodeStartX,
				y: zoneConfigs.general.baseY + zoneConfigs.general.nodeStartY
			},
			data: {
				title: 'Players',
				icon: '👥',
				color: '#DF6975',
				zone: 'general',
				currentValue: 8,
				isDefault: true,
				parentZoneTimestamp: 'default-general',
				onDelete: handleIndividualNodeDelete
			},
			deletable: false,
			draggable: false
		},
		{
			id: 'team-size-setting',
			type: 'individualSetting',
			position: {
				x: zoneConfigs.general.baseX + zoneConfigs.general.nodeStartX + zoneConfigs.general.nodeSpacing.x,
				y: zoneConfigs.general.baseY + zoneConfigs.general.nodeStartY
			},
			data: {
				title: 'Team Size',
				icon: '👫',
				color: '#DF6975',
				zone: 'general',
				currentValue: 1,
				isDefault: true,
				parentZoneTimestamp: 'default-general',
				onDelete: handleIndividualNodeDelete
			},
			deletable: false,
			draggable: false
		},
		{
			id: 'songs-setting',
			type: 'individualSetting',
			position: {
				x: zoneConfigs.general.baseX + zoneConfigs.general.nodeStartX,
				y: zoneConfigs.general.baseY + zoneConfigs.general.nodeStartY + zoneConfigs.general.nodeSpacing.y
			},
			data: {
				title: 'Songs',
				icon: '🎵',
				color: '#DF6975',
				zone: 'general',
				currentValue: 20,
				isDefault: true
			},
			deletable: false,
			draggable: false
		},
		{
			id: 'song-selection-setting',
			type: 'individualSetting',
			position: {
				x: zoneConfigs.general.baseX + zoneConfigs.general.nodeStartX + zoneConfigs.general.nodeSpacing.x,
				y: zoneConfigs.general.baseY + zoneConfigs.general.nodeStartY + zoneConfigs.general.nodeSpacing.y
			},
			data: {
				title: 'Song Selection',
				icon: '🎲',
				color: '#DF6975',
				zone: 'general',
				currentValue: { random: 0, mix: 0, watched: 100 },
				isDefault: true
			},
			deletable: false,
			draggable: false
		},
		{
			id: 'watched-distribution-setting',
			type: 'individualSetting',
			position: {
				x: zoneConfigs.general.baseX + zoneConfigs.general.nodeStartX,
				y: zoneConfigs.general.baseY + zoneConfigs.general.nodeStartY + (zoneConfigs.general.nodeSpacing.y * 2)
			},
			data: {
				title: 'Watched Distribution',
				icon: '📊',
				color: '#DF6975',
				zone: 'general',
				currentValue: 'random',
				isDefault: true
			},
			deletable: false,
			draggable: false
		},
		{
			id: 'song-types-setting',
			type: 'individualSetting',
			position: {
				x: zoneConfigs.general.baseX + zoneConfigs.general.nodeStartX + zoneConfigs.general.nodeSpacing.x,
				y: zoneConfigs.general.baseY + zoneConfigs.general.nodeStartY + (zoneConfigs.general.nodeSpacing.y * 2)
			},
			data: {
				title: 'Song Types',
				icon: '🎼',
				color: '#DF6975',
				zone: 'general',
				currentValue: { openings: 100, endings: 100, inserts: 0 },
				isDefault: true
			},
			deletable: false,
			draggable: false
		},
		{
			id: 'openings-categories-setting',
			type: 'individualSetting',
			position: {
				x: zoneConfigs.general.baseX + zoneConfigs.general.nodeStartX,
				y: zoneConfigs.general.baseY + zoneConfigs.general.nodeStartY + (zoneConfigs.general.nodeSpacing.y * 3)
			},
			data: {
				title: 'Openings Categories',
				icon: '🎬',
				color: '#DF6975',
				zone: 'general',
				currentValue: { standard: true, instrumental: true, chanting: true, character: true },
				isDefault: true
			},
			deletable: false,
			draggable: false
		},
		{
			id: 'endings-categories-setting',
			type: 'individualSetting',
			position: {
				x: zoneConfigs.general.baseX + zoneConfigs.general.nodeStartX + zoneConfigs.general.nodeSpacing.x,
				y: zoneConfigs.general.baseY + zoneConfigs.general.nodeStartY + (zoneConfigs.general.nodeSpacing.y * 3)
			},
			data: {
				title: 'Endings Categories',
				icon: '🎭',
				color: '#DF6975',
				zone: 'general',
				currentValue: { standard: true, instrumental: true, chanting: true, character: true },
				isDefault: true
			},
			deletable: false,
			draggable: false
		},
		{
			id: 'inserts-categories-setting',
			type: 'individualSetting',
			position: {
				x: zoneConfigs.general.baseX + zoneConfigs.general.nodeStartX,
				y: zoneConfigs.general.baseY + zoneConfigs.general.nodeStartY + (zoneConfigs.general.nodeSpacing.y * 4)
			},
			data: {
				title: 'Inserts Categories',
				icon: '🎪',
				color: '#DF6975',
				zone: 'general',
				currentValue: { standard: true, instrumental: true, chanting: true, character: true },
				isDefault: true
			},
			deletable: false,
			draggable: false
		},

		// Individual Setting Nodes - Quiz Zone (7 nodes) - 2 columns tower layout
		{
			id: 'guess-time-setting',
			type: 'individualSetting',
			position: {
				x: zoneConfigs.quiz.baseX + zoneConfigs.quiz.nodeStartX,
				y: zoneConfigs.quiz.baseY + zoneConfigs.quiz.nodeStartY
			},
			data: {
				title: 'Guess Time',
				icon: '⏰',
				color: '#75DF8B',
				zone: 'quiz',
				currentValue: 20,
				isDefault: true
			},
			deletable: false,
			draggable: false
		},
		{
			id: 'extra-time-setting',
			type: 'individualSetting',
			position: {
				x: zoneConfigs.quiz.baseX + zoneConfigs.quiz.nodeStartX + zoneConfigs.quiz.nodeSpacing.x,
				y: zoneConfigs.quiz.baseY + zoneConfigs.quiz.nodeStartY
			},
			data: {
				title: 'Extra Time',
				icon: '⏱️',
				color: '#75DF8B',
				zone: 'quiz',
				currentValue: 0,
				isDefault: true
			},
			deletable: false,
			draggable: false
		},
		{
			id: 'sample-point-setting',
			type: 'individualSetting',
			position: {
				x: zoneConfigs.quiz.baseX + zoneConfigs.quiz.nodeStartX,
				y: zoneConfigs.quiz.baseY + zoneConfigs.quiz.nodeStartY + zoneConfigs.quiz.nodeSpacing.y
			},
			data: {
				title: 'Sample Point',
				icon: '🎯',
				color: '#75DF8B',
				zone: 'quiz',
				currentValue: { start: 0, end: 100 },
				isDefault: true
			},
			deletable: false,
			draggable: false
		},
		{
			id: 'playback-speed-setting',
			type: 'individualSetting',
			position: {
				x: zoneConfigs.quiz.baseX + zoneConfigs.quiz.nodeStartX + zoneConfigs.quiz.nodeSpacing.x,
				y: zoneConfigs.quiz.baseY + zoneConfigs.quiz.nodeStartY + zoneConfigs.quiz.nodeSpacing.y
			},
			data: {
				title: 'Playback Speed',
				icon: '⚡',
				color: '#75DF8B',
				zone: 'quiz',
				currentValue: 1.0,
				isDefault: true
			},
			deletable: false,
			draggable: false
		},
		{
			id: 'song-difficulty-setting',
			type: 'individualSetting',
			position: {
				x: zoneConfigs.quiz.baseX + zoneConfigs.quiz.nodeStartX,
				y: zoneConfigs.quiz.baseY + zoneConfigs.quiz.nodeStartY + (zoneConfigs.quiz.nodeSpacing.y * 2)
			},
			data: {
				title: 'Song Difficulty',
				icon: '📈',
				color: '#75DF8B',
				zone: 'quiz',
				currentValue: { easy: true, medium: true, hard: true },
				isDefault: true
			},
			deletable: false,
			draggable: false
		},
		{
			id: 'song-popularity-setting',
			type: 'individualSetting',
			position: {
				x: zoneConfigs.quiz.baseX + zoneConfigs.quiz.nodeStartX + zoneConfigs.quiz.nodeSpacing.x,
				y: zoneConfigs.quiz.baseY + zoneConfigs.quiz.nodeStartY + (zoneConfigs.quiz.nodeSpacing.y * 2)
			},
			data: {
				title: 'Song Popularity',
				icon: '⭐',
				color: '#75DF8B',
				zone: 'quiz',
				currentValue: { disliked: true, mixed: true, liked: true },
				isDefault: true
			},
			deletable: false,
			draggable: false
		},
		{
			id: 'modifiers-setting',
			type: 'individualSetting',
			position: {
				x: zoneConfigs.quiz.baseX + zoneConfigs.quiz.nodeStartX,
				y: zoneConfigs.quiz.baseY + zoneConfigs.quiz.nodeStartY + (zoneConfigs.quiz.nodeSpacing.y * 3)
			},
			data: {
				title: 'Modifiers',
				icon: '🔧',
				color: '#75DF8B',
				zone: 'quiz',
				currentValue: { skipGuessing: true, skipResults: true, queueing: true },
				isDefault: true
			},
			deletable: false,
			draggable: false
		},

		// Individual Setting Nodes - Anime Zone (6 nodes) - 2 columns tower layout
		{
			id: 'player-score-setting',
			type: 'individualSetting',
			position: {
				x: zoneConfigs.anime.baseX + zoneConfigs.anime.nodeStartX,
				y: zoneConfigs.anime.baseY + zoneConfigs.anime.nodeStartY
			},
			data: {
				title: 'Player Score',
				icon: '⭐',
				color: '#DFB975',
				zone: 'anime',
				currentValue: { min: 1, max: 10 },
				isDefault: true
			},
			deletable: false,
			draggable: false
		},
		{
			id: 'anime-score-setting',
			type: 'individualSetting',
			position: {
				x: zoneConfigs.anime.baseX + zoneConfigs.anime.nodeStartX + zoneConfigs.anime.nodeSpacing.x,
				y: zoneConfigs.anime.baseY + zoneConfigs.anime.nodeStartY
			},
			data: {
				title: 'Anime Score',
				icon: '📊',
				color: '#DFB975',
				zone: 'anime',
				currentValue: { min: 2, max: 10 },
				isDefault: true
			},
			deletable: false,
			draggable: false
		},
		{
			id: 'vintage-setting',
			type: 'individualSetting',
			position: {
				x: zoneConfigs.anime.baseX + zoneConfigs.anime.nodeStartX,
				y: zoneConfigs.anime.baseY + zoneConfigs.anime.nodeStartY + zoneConfigs.anime.nodeSpacing.y
			},
			data: {
				title: 'Vintage',
				icon: '📅',
				color: '#DFB975',
				zone: 'anime',
				currentValue: { from: 'Winter 1944', to: 'Fall 2025' },
				isDefault: true
			},
			deletable: false,
			draggable: false
		},
		{
			id: 'anime-type-setting',
			type: 'individualSetting',
			position: {
				x: zoneConfigs.anime.baseX + zoneConfigs.anime.nodeStartX + zoneConfigs.anime.nodeSpacing.x,
				y: zoneConfigs.anime.baseY + zoneConfigs.anime.nodeStartY + zoneConfigs.anime.nodeSpacing.y
			},
			data: {
				title: 'Anime Type',
				icon: '🎬',
				color: '#DFB975',
				zone: 'anime',
				currentValue: { tv: true, movie: true, ova: true, ona: true, special: true },
				isDefault: true
			},
			deletable: false,
			draggable: false
		},
		{
			id: 'genres-setting',
			type: 'individualSetting',
			position: {
				x: zoneConfigs.anime.baseX + zoneConfigs.anime.nodeStartX,
				y: zoneConfigs.anime.baseY + zoneConfigs.anime.nodeStartY + (zoneConfigs.anime.nodeSpacing.y * 2)
			},
			data: {
				title: 'Genres',
				icon: '🏷️',
				color: '#DFB975',
				zone: 'anime',
				currentValue: { include: [], exclude: [], optional: [] },
				isDefault: true
			},
			deletable: false,
			draggable: false
		},
		{
			id: 'tags-setting',
			type: 'individualSetting',
			position: {
				x: zoneConfigs.anime.baseX + zoneConfigs.anime.nodeStartX + zoneConfigs.anime.nodeSpacing.x,
				y: zoneConfigs.anime.baseY + zoneConfigs.anime.nodeStartY + (zoneConfigs.anime.nodeSpacing.y * 2)
			},
			data: {
				title: 'Tags',
				icon: '🔖',
				color: '#DFB975',
				zone: 'anime',
				currentValue: { include: [], exclude: [], optional: [] },
				isDefault: true
			},
			deletable: false,
			draggable: false
		}
	];

	// Define initial edges (connections between nodes)
	let initialEdges = [
		// Zone flow connections
		{
			id: 'e-mode-general',
			source: 'mode-zone',
			target: 'general-zone',
			type: 'step',
			style: 'stroke: #75B9DF; stroke-width: 3; stroke-dasharray: 8,4;',
			animated: true
		},
		{
			id: 'e-general-quiz',
			source: 'general-zone',
			target: 'quiz-zone',
			type: 'step',
			style: 'stroke: #DF6975; stroke-width: 3; stroke-dasharray: 8,4;',
			animated: true
		},
		{
			id: 'e-quiz-anime',
			source: 'quiz-zone',
			target: 'anime-zone',
			type: 'step',
			style: 'stroke: #75DF8B; stroke-width: 3; stroke-dasharray: 8,4;',
			animated: true
		},

		// Individual setting connections to zones - Mode
		{
			id: 'e-scoring-mode',
			source: 'scoring-setting',
			target: 'mode-zone',
			targetHandle: 'settings-input',
			type: 'step',
			style: 'stroke: #75B9DF; stroke-width: 2;',
			animated: false
		},
		{
			id: 'e-answering-mode',
			source: 'answering-setting',
			target: 'mode-zone',
			targetHandle: 'settings-input',
			type: 'step',
			style: 'stroke: #75B9DF; stroke-width: 2;',
			animated: false
		},

		// Individual setting connections to zones - General
		{
			id: 'e-players-general',
			source: 'players-setting',
			target: 'general-zone',
			targetHandle: 'settings-input',
			type: 'step',
			style: 'stroke: #DF6975; stroke-width: 2;',
			animated: false
		},
		{
			id: 'e-team-size-general',
			source: 'team-size-setting',
			target: 'general-zone',
			targetHandle: 'settings-input',
			type: 'step',
			style: 'stroke: #DF6975; stroke-width: 2;',
			animated: false
		},
		{
			id: 'e-songs-general',
			source: 'songs-setting',
			target: 'general-zone',
			targetHandle: 'settings-input',
			type: 'step',
			style: 'stroke: #DF6975; stroke-width: 2;',
			animated: false
		},
		{
			id: 'e-song-selection-general',
			source: 'song-selection-setting',
			target: 'general-zone',
			targetHandle: 'settings-input',
			type: 'step',
			style: 'stroke: #DF6975; stroke-width: 2;',
			animated: false
		},
		{
			id: 'e-watched-distribution-general',
			source: 'watched-distribution-setting',
			target: 'general-zone',
			targetHandle: 'settings-input',
			type: 'step',
			style: 'stroke: #DF6975; stroke-width: 2;',
			animated: false
		},
		{
			id: 'e-song-types-general',
			source: 'song-types-setting',
			target: 'general-zone',
			targetHandle: 'settings-input',
			type: 'step',
			style: 'stroke: #DF6975; stroke-width: 2;',
			animated: false
		},
		{
			id: 'e-openings-categories-general',
			source: 'openings-categories-setting',
			target: 'general-zone',
			targetHandle: 'settings-input',
			type: 'step',
			style: 'stroke: #DF6975; stroke-width: 2;',
			animated: false
		},
		{
			id: 'e-endings-categories-general',
			source: 'endings-categories-setting',
			target: 'general-zone',
			targetHandle: 'settings-input',
			type: 'step',
			style: 'stroke: #DF6975; stroke-width: 2;',
			animated: false
		},
		{
			id: 'e-inserts-categories-general',
			source: 'inserts-categories-setting',
			target: 'general-zone',
			targetHandle: 'settings-input',
			type: 'step',
			style: 'stroke: #DF6975; stroke-width: 2;',
			animated: false
		},

		// Individual setting connections to zones - Quiz
		{
			id: 'e-guess-time-quiz',
			source: 'guess-time-setting',
			target: 'quiz-zone',
			targetHandle: 'settings-input',
			type: 'step',
			style: 'stroke: #75DF8B; stroke-width: 2;',
			animated: false
		},
		{
			id: 'e-extra-time-quiz',
			source: 'extra-time-setting',
			target: 'quiz-zone',
			targetHandle: 'settings-input',
			type: 'step',
			style: 'stroke: #75DF8B; stroke-width: 2;',
			animated: false
		},
		{
			id: 'e-sample-point-quiz',
			source: 'sample-point-setting',
			target: 'quiz-zone',
			targetHandle: 'settings-input',
			type: 'step',
			style: 'stroke: #75DF8B; stroke-width: 2;',
			animated: false
		},
		{
			id: 'e-playback-speed-quiz',
			source: 'playback-speed-setting',
			target: 'quiz-zone',
			targetHandle: 'settings-input',
			type: 'step',
			style: 'stroke: #75DF8B; stroke-width: 2;',
			animated: false
		},
		{
			id: 'e-song-difficulty-quiz',
			source: 'song-difficulty-setting',
			target: 'quiz-zone',
			targetHandle: 'settings-input',
			type: 'step',
			style: 'stroke: #75DF8B; stroke-width: 2;',
			animated: false
		},
		{
			id: 'e-song-popularity-quiz',
			source: 'song-popularity-setting',
			target: 'quiz-zone',
			targetHandle: 'settings-input',
			type: 'step',
			style: 'stroke: #75DF8B; stroke-width: 2;',
			animated: false
		},
		{
			id: 'e-modifiers-quiz',
			source: 'modifiers-setting',
			target: 'quiz-zone',
			targetHandle: 'settings-input',
			type: 'step',
			style: 'stroke: #75DF8B; stroke-width: 2;',
			animated: false
		},

		// Individual setting connections to zones - Anime
		{
			id: 'e-player-score-anime',
			source: 'player-score-setting',
			target: 'anime-zone',
			targetHandle: 'settings-input',
			type: 'step',
			style: 'stroke: #DFB975; stroke-width: 2;',
			animated: false
		},
		{
			id: 'e-anime-score-anime',
			source: 'anime-score-setting',
			target: 'anime-zone',
			targetHandle: 'settings-input',
			type: 'step',
			style: 'stroke: #DFB975; stroke-width: 2;',
			animated: false
		},
		{
			id: 'e-vintage-anime',
			source: 'vintage-setting',
			target: 'anime-zone',
			targetHandle: 'settings-input',
			type: 'step',
			style: 'stroke: #DFB975; stroke-width: 2;',
			animated: false
		},
		{
			id: 'e-anime-type-anime',
			source: 'anime-type-setting',
			target: 'anime-zone',
			targetHandle: 'settings-input',
			type: 'step',
			style: 'stroke: #DFB975; stroke-width: 2;',
			animated: false
		},
		{
			id: 'e-genres-anime',
			source: 'genres-setting',
			target: 'anime-zone',
			targetHandle: 'settings-input',
			type: 'step',
			style: 'stroke: #DFB975; stroke-width: 2;',
			animated: false
		},
		{
			id: 'e-tags-anime',
			source: 'tags-setting',
			target: 'anime-zone',
			targetHandle: 'settings-input',
			type: 'step',
			style: 'stroke: #DFB975; stroke-width: 2;',
			animated: false
		}
	];

	// Reactive variables for the flow
	let nodes = $state(initialNodes);
	let edges = $state.raw(initialEdges);

	// Update zone backgrounds after initialization to ensure proper positioning
	updateZoneBackgrounds();

	// UI state
	let sidePanelOpen = $state(false);
	let nodeSidebarOpen = $state(false);

	// Update zone summaries when individual settings change (removed to prevent infinite loop)
	// This will be handled manually when settings actually change

	// Helper functions for future manual updates
	function getConnectedSettings(zone) {
		const settingNodes = nodes.filter(node =>
			node.type === 'individualSetting' && node.data.zone === zone
		);

		const settings = {};
		settingNodes.forEach(node => {
			const settingKey = getSettingKey(node.data.title);
			settings[settingKey] = node.data.currentValue;
		});

		return settings;
	}

	function getSettingKey(title) {
		const keyMap = {
			'Scoring': 'scoring',
			'Answering': 'answering',
			'Players': 'numberOfPlayers',
			'Songs': 'numberOfSongs',
			'Team Size': 'teamSize',
			'Guess Time': 'guessTime',
			'Extra Time': 'extraGuessTime',
			'Playback Speed': 'playbackSpeed',
			'Player Score': 'playerScore',
			'Anime Score': 'animeScore',
			'Vintage': 'vintage'
		};
		return keyMap[title] || title.toLowerCase().replace(/\s+/g, '');
	}

	// Manual update function (call when needed)
	function updateZoneSummaries() {
		nodes = nodes.map(node => {
			if (node.type === 'zoneArea') {
				const connectedSettings = getConnectedSettings(node.data.zone);
				return {
					...node,
					data: {
						...node.data,
						connectedSettings
					}
				};
			}
			return node;
		});
	}




	// Event handlers
	const onNodeDrag = (event) => {
		// SvelteFlow passes the node in event.targetNode
		if (!event || !event.targetNode) {
			console.warn('onNodeDrag: Could not find targetNode in event', event);
			return;
		}

		const node = event.targetNode;

		// If a zone area node is being dragged, move all other nodes in that zone (background, label, individual nodes)
		// The zone area node itself moves naturally with the drag
		if (node.type === 'zoneArea') {
			const zone = node.data.zone;
			const config = zoneConfigs[zone];
			const originalPos = originalZonePositions[zone];

			if (originalPos) {
				// Calculate the offset from the zone area node's original position
				const deltaX = node.position.x - originalPos.zoneAreaX;
				const deltaY = node.position.y - originalPos.zoneAreaY;

				// Update the zone's base position
				const newBaseX = originalPos.baseX + deltaX;
				const newBaseY = originalPos.baseY + deltaY;

				// Update all nodes in this zone, including the zone area node to ensure consistency
				nodes = nodes.map(n => {
					// Update the zone area node position to match the dragged position
					if (n.id === node.id) {
						return {
							...n,
							position: {
								x: node.position.x,
								y: node.position.y
							}
						};
					}

					// Move zone background
					if (n.id === `${zone}-zone-bg`) {
						return {
							...n,
							position: {
								x: newBaseX,
								y: newBaseY
							}
						};
					}
					// Move zone label
					else if (n.id === `${zone}-zone-label`) {
						return {
							...n,
							position: {
								x: newBaseX + 10,
								y: newBaseY + 10
							}
						};
					}
					// Move individual setting nodes
					else if (n.type === 'individualSetting' && n.data.zone === zone) {
						// Find this node's index among nodes in the same zone
						const zoneNodes = nodes.filter(zoneNode =>
							zoneNode.type === 'individualSetting' && zoneNode.data.zone === zone
						);
						const nodeIndex = zoneNodes.findIndex(zn => zn.id === n.id);

						if (nodeIndex !== -1) {
							const row = Math.floor(nodeIndex / config.columns);
							const col = nodeIndex % config.columns;

							return {
								...n,
								position: {
									x: newBaseX + config.nodeStartX + (col * config.nodeSpacing.x),
									y: newBaseY + config.nodeStartY + (row * config.nodeSpacing.y)
								}
							};
						}
					}
					// Move zone delete button
					else if (n.id.includes(`${zone}-zone-delete-btn`)) {
						return {
							...n,
							position: {
								x: newBaseX,
								y: newBaseY
							}
						};
					}
					return n;
				});
			}
		}
	};

	const onNodeDragStop = (event) => {
		// SvelteFlow passes the node in event.targetNode
		if (!event || !event.targetNode) {
			console.warn('onNodeDragStop: Could not find targetNode in event', event);
			return;
		}

		const node = event.targetNode;

		// If a zone area node was dragged, update the zone configuration with new positions
		if (node.type === 'zoneArea') {
			const zone = node.data.zone;
			const config = zoneConfigs[zone];
			const originalPos = originalZonePositions[zone];

			if (originalPos) {
				// Calculate the offset from the zone area node's original position
				const deltaX = node.position.x - originalPos.zoneAreaX;
				const deltaY = node.position.y - originalPos.zoneAreaY;

				// Update the zone's base position in the configuration
				config.baseX = originalPos.baseX + deltaX;
				config.baseY = originalPos.baseY + deltaY;

				// Update the original positions for future drags
				const dimensions = calculateZoneDimensions(config);
				originalZonePositions[zone] = {
					baseX: config.baseX,
					baseY: config.baseY,
					zoneAreaX: config.baseX + (dimensions.bgWidth - 384) / 2,
					zoneAreaY: config.baseY + config.zoneAreaY
				};
			}
		}

		console.log('Node drag stopped:', event);
	};



	const onNodeClick = (event) => {
		console.log('Node clicked:', event.detail);
	};

	const onEdgeClick = (event) => {
		console.log('Edge clicked:', event.detail);
	};

	// Prevent deletion of zone areas and individual setting nodes
	const onNodesDelete = (event) => {
		console.log('Attempted to delete nodes:', event.detail);
		// Prevent deletion by returning false
		event.preventDefault?.();
		return false;
	};

	// Prevent disconnection of edges from zone areas
	const onEdgesDelete = (event) => {
		console.log('Attempted to delete edges:', event.detail);
		// Prevent deletion by returning false
		event.preventDefault?.();
		return false;
	};

	// Toggle side panel
	const toggleSidePanel = () => {
		sidePanelOpen = !sidePanelOpen;
	};

	// Export configuration
	const exportConfig = () => {
		const config = {
			nodes: nodes.map(node => ({
				id: node.id,
				zone: node.data.zone,
				settings: node.data.settings
			})),
			timestamp: new Date().toISOString()
		};
		
		const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' });
		const url = URL.createObjectURL(blob);
		const a = document.createElement('a');
		a.href = url;
		a.download = 'amq-lobby-config.json';
		a.click();
		URL.revokeObjectURL(url);
	};

	// Reset to defaults with confirmation
	const resetToDefaults = () => {
		const confirmed = confirm(
			"Are you sure you want to restore everything to defaults?\n\n" +
			"This will:\n" +
			"• Reset all node positions\n" +
			"• Remove any custom nodes you've added\n" +
			"• Reset all settings to their initial values\n" +
			"• Clear any custom configurations\n\n" +
			"This action cannot be undone."
		);

		if (confirmed) {
			nodes = [...initialNodes];
			edges = [...initialEdges];

			// Recalculate zone positions and update backgrounds
			calculateZonePositions();
			updateZoneBackgrounds();
		}
	};

	// Add entire zone from sidebar
	const handleAddNode = (zoneKey, zoneData) => {
		// Check if zone already exists (including default zones)
		const existingZone = nodes.find(node =>
			node.type === 'zoneArea' && node.data.zone === zoneKey
		);

		if (existingZone) {
			alert(`The ${zoneKey} zone already exists. You cannot add duplicate zones.`);
			return;
		}

		const zoneColors = {
			mode: '#75B9DF',
			general: '#DF6975',
			quiz: '#75DF8B',
			anime: '#DFB975'
		};

		const timestamp = Date.now();
		const newNodes = [];
		const newEdges = [];

		// Calculate position for the new zone (place it to the right of existing zones)
		const existingZones = nodes.filter(n => n.type === 'zoneArea');
		const rightmostZone = existingZones.reduce((max, zone) =>
			zone.position.x > max ? zone.position.x : max, 0
		);

		// Calculate new zone position
		const newZoneBaseX = rightmostZone > 0 ? rightmostZone + 500 : 50; // 500px spacing between zones
		const newZoneBaseY = 50;

		// Create zone background
		const zoneBgId = `${zoneKey}-zone-bg-${timestamp}`;
		const zoneDimensions = calculateZoneDimensions({
			...zoneConfigs[zoneKey],
			baseX: newZoneBaseX,
			baseY: newZoneBaseY,
			nodeCount: zoneData.nodes.length
		});

		newNodes.push({
			id: zoneBgId,
			type: 'default',
			position: { x: newZoneBaseX, y: newZoneBaseY },
			data: { label: '' },
			style: `background: ${zoneColors[zoneKey].replace('#', 'rgba(').replace(/(.{2})(.{2})(.{2})/, '$1, $2, $3')}, 0.05); border: 2px dashed ${zoneColors[zoneKey].replace('#', 'rgba(').replace(/(.{2})(.{2})(.{2})/, '$1, $2, $3')}, 0.3); border-radius: 12px; width: ${zoneDimensions.bgWidth}px; height: ${zoneDimensions.bgHeight}px; pointer-events: none;`,
			selectable: false,
			draggable: false,
			deletable: false
		});

		// Create zone label
		const zoneLabelId = `${zoneKey}-zone-label-${timestamp}`;
		newNodes.push({
			id: zoneLabelId,
			type: 'default',
			position: { x: newZoneBaseX + 10, y: newZoneBaseY + 10 },
			data: { label: `${zoneData.icon} ${zoneData.title}` },
			style: `background: transparent; border: none; color: ${zoneColors[zoneKey].replace('#', 'rgba(').replace(/(.{2})(.{2})(.{2})/, '$1, $2, $3')}, 0.8); font-weight: bold; font-size: 14px; pointer-events: none;`,
			selectable: false,
			draggable: false,
			deletable: false
		});

		// Create zone delete button for individual nodes area
		const zoneDeleteBtnId = `${zoneKey}-zone-delete-btn-${timestamp}`;
		const individualNodesAreaDimensions = calculateIndividualNodesAreaDimensions({
			...zoneConfigs[zoneKey],
			nodeCount: zoneData.nodes.length
		});

		newNodes.push({
			id: zoneDeleteBtnId,
			type: 'zoneDeleteButton',
			position: {
				x: newZoneBaseX,
				y: newZoneBaseY
			},
			data: {
				zone: zoneKey,
				timestamp: timestamp,
				width: individualNodesAreaDimensions.width,
				height: individualNodesAreaDimensions.height,
				onDelete: handleZoneDelete
			},
			selectable: false,
			draggable: false,
			deletable: false
		});

		// Create zone area node (main container)
		const zoneAreaId = `${zoneKey}-zone-${timestamp}`;
		newNodes.push({
			id: zoneAreaId,
			type: 'zoneArea',
			position: {
				x: newZoneBaseX + (zoneDimensions.bgWidth - 384) / 2,
				y: newZoneBaseY + zoneConfigs[zoneKey].zoneAreaY
			},
			data: {
				title: zoneData.title,
				description: zoneData.description,
				icon: zoneData.icon,
				color: zoneColors[zoneKey],
				zone: zoneKey,
				connectedSettings: {},
				timestamp: timestamp
			},
			deletable: false,
			draggable: true
		});

		// Create individual setting nodes
		zoneData.nodes.forEach((nodeData, index) => {
			const row = Math.floor(index / zoneConfigs[zoneKey].columns);
			const col = index % zoneConfigs[zoneKey].columns;

			const nodeId = `${nodeData.id}-${timestamp}`;
			const nodePosition = {
				x: newZoneBaseX + zoneConfigs[zoneKey].nodeStartX + (col * zoneConfigs[zoneKey].nodeSpacing.x),
				y: newZoneBaseY + zoneConfigs[zoneKey].nodeStartY + (row * zoneConfigs[zoneKey].nodeSpacing.y)
			};

			newNodes.push({
				id: nodeId,
				type: 'individualSetting',
				position: nodePosition,
				data: {
					title: nodeData.title,
					icon: nodeData.icon,
					color: zoneColors[zoneKey],
					zone: zoneKey,
					currentValue: getDefaultValue(nodeData.id),
					isDefault: false,
					parentZoneTimestamp: timestamp,
					onDelete: handleIndividualNodeDelete
				},
				deletable: true,
				draggable: false
			});

			// Create edge connecting individual node to zone area
			newEdges.push({
				id: `e-${nodeId}-${zoneAreaId}`,
				source: nodeId,
				target: zoneAreaId,
				targetHandle: 'settings-input',
				type: 'step',
				style: `stroke: ${zoneColors[zoneKey]}; stroke-width: 2;`,
				animated: false
			});
		});

		// Add all new nodes and edges
		nodes = [...nodes, ...newNodes];
		edges = [...edges, ...newEdges];

		// Update zone backgrounds to ensure proper positioning
		updateZoneBackgrounds();

		// Close sidebar after adding
		nodeSidebarOpen = false;
	};

	// Get default value for a setting
	function getDefaultValue(settingId) {
		const defaults = {
			'scoring': 'count',
			'answering': 'typing',
			'lives': 5,
			'skip-votes': 3,
			'players': 8,
			'songs': 20,
			'team-size': 1,
			'song-selection': 'watched',
			'watched-distribution': 'random',
			'guess-time': 20,
			'extra-time': 0,
			'playback-speed': 1.0,
			'sample-point': { start: 0, end: 100 },
			'difficulty': { easy: true, medium: true, hard: true },
			'popularity': { disliked: true, mixed: true, liked: true },
			'player-score': { min: 1, max: 10 },
			'anime-score': { min: 2, max: 10 },
			'vintage': { from: 'Winter 1944', to: 'Fall 2025' },
			'type': { tv: true, movie: true, ova: true, ona: true, special: true },
			'genres': { include: [], exclude: [], optional: [] },
			'tags': { include: [], exclude: [], optional: [] }
		};
		return defaults[settingId] || 'Custom';
	}



	// Apply automatic layout (reset positions only, keep custom nodes and settings)
	const applyAutoLayout = () => {
		// First recalculate zone positions to get proper defaults
		calculateZonePositions();

		// Update zone backgrounds to ensure proper positioning
		updateZoneBackgrounds();

		// Reset all node positions to their default calculated positions
		// This keeps any custom nodes that were added but resets their positions
		nodes = nodes.map(node => {
			// For zone backgrounds, use current zone config
			if (node.id.endsWith('-zone-bg')) {
				const zone = node.id.replace('-zone-bg', '');
				const config = zoneConfigs[zone];
				return {
					...node,
					position: { x: config.baseX, y: config.baseY }
				};
			}
			// For zone labels
			else if (node.id.endsWith('-zone-label')) {
				const zone = node.id.replace('-zone-label', '');
				const config = zoneConfigs[zone];
				return {
					...node,
					position: { x: config.baseX + 10, y: config.baseY + 10 }
				};
			}
			// For zone area nodes
			else if (node.type === 'zoneArea') {
				const zone = node.data.zone;
				const config = zoneConfigs[zone];
				const dimensions = calculateZoneDimensions(config);
				return {
					...node,
					position: {
						x: config.baseX + (dimensions.bgWidth - 384) / 2,
						y: config.baseY + config.zoneAreaY
					}
				};
			}
			// For zone delete buttons
			else if (node.id.endsWith('-zone-delete-btn')) {
				const zone = node.id.replace('-zone-delete-btn', '').split('-').slice(0, -1).join('-');
				const config = zoneConfigs[zone];
				if (config) {
					const actualNodeCount = nodes.filter(n =>
						n.type === 'individualSetting' && n.data.zone === zone
					).length;
					const individualNodesAreaDimensions = calculateIndividualNodesAreaDimensions(config, actualNodeCount);

					return {
						...node,
						position: { x: config.baseX, y: config.baseY },
						data: {
							...node.data,
							width: individualNodesAreaDimensions.width,
							height: individualNodesAreaDimensions.height
						}
					};
				}
			}
			// For individual setting nodes (including custom ones)
			else if (node.type === 'individualSetting') {
				const zone = node.data.zone;
				const config = zoneConfigs[zone];
				const zoneNodes = nodes.filter(n =>
					n.type === 'individualSetting' && n.data.zone === zone
				);
				const nodeIndex = zoneNodes.findIndex(n => n.id === node.id);

				if (nodeIndex !== -1) {
					const row = Math.floor(nodeIndex / config.columns);
					const col = nodeIndex % config.columns;

					return {
						...node,
						position: {
							x: config.baseX + config.nodeStartX + (col * config.nodeSpacing.x),
							y: config.baseY + config.nodeStartY + (row * config.nodeSpacing.y)
						}
					};
				}
			}

			return node;
		});
	};
</script>

<svelte:head>
	<title>AMQ PLUS - Node Editor</title>
	<meta name="description" content="Configure your AMQ lobby settings with our visual node-based editor" />
</svelte:head>

<!-- Fullscreen Node Editor -->
<div class="relative w-full h-full">
	<!-- Side Panel Toggle Button -->
	<button
		onclick={toggleSidePanel}
		class="fixed z-50 p-2 transition-colors border rounded-lg shadow-lg top-4 left-4 bg-white/95 backdrop-blur-sm border-amq-light hover:bg-white"
		title={sidePanelOpen ? 'Close panel' : 'Open panel'}
		aria-label={sidePanelOpen ? 'Close panel' : 'Open panel'}
	>
		<svg
			class="w-5 h-5 text-gray-600 transition-transform duration-200 {sidePanelOpen ? 'rotate-180' : ''}"
			fill="none"
			stroke="currentColor"
			viewBox="0 0 24 24"
		>
			<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
		</svg>
	</button>

	<!-- Side Panel -->
	<div class="fixed top-0 left-0 h-full z-40 transition-transform duration-300 ease-in-out {sidePanelOpen ? 'translate-x-0' : '-translate-x-full'}">
		<div class="flex flex-col h-full border-r shadow-xl w-80 bg-white/95 backdrop-blur-sm border-amq-light">
			<!-- Panel Header -->
			<div class="p-6 border-b border-amq-light">
				<div class="flex items-center mb-4 space-x-3">
					<a href="/" class="text-2xl font-bold amq-gradient-text">AMQ PLUS</a>
					<span class="text-gray-400">|</span>
					<h1 class="text-lg font-semibold text-gray-800">Node Editor</h1>
				</div>
				<p class="text-sm text-gray-600">
					Configure your AMQ lobby settings with our visual node-based editor.
				</p>
			</div>

			<!-- Panel Content -->
			<div class="flex-1 p-6 space-y-6">
				<!-- Actions Section -->
				<div class="space-y-4">
					<h3 class="text-sm font-semibold tracking-wide text-gray-800 uppercase">Actions</h3>
					<div class="space-y-3">
						<Button
							onclick={() => nodeSidebarOpen = true}
							variant="outline"
							class="justify-start w-full"
						>
							<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
							</svg>
							Add Nodes
						</Button>
						<Button
							onclick={applyAutoLayout}
							variant="outline"
							class="justify-start w-full"
						>
							<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
							</svg>
							Auto Layout
						</Button>
						<Button
							onclick={resetToDefaults}
							variant="outline"
							class="justify-start w-full"
						>
							<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
							</svg>
							Reset to Defaults
						</Button>
						<Button
							onclick={exportConfig}
							class="justify-start w-full text-white bg-amq-primary hover:bg-amq-dark"
						>
							<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
							</svg>
							Export Configuration
						</Button>
					</div>
				</div>

				<!-- Info Section -->
				<div class="space-y-4">
					<h3 class="text-sm font-semibold tracking-wide text-gray-800 uppercase">Instructions</h3>
					<div class="space-y-2 text-sm text-gray-600">
						<p>• Drag nodes to reposition them</p>
						<p>• Click on nodes to edit settings</p>
						<p>• Use controls to zoom and pan</p>
						<p>• Export when ready to use</p>
					</div>
				</div>
			</div>
		</div>
	</div>

	<!-- Fullscreen Editor -->
	<main class="absolute inset-0 w-full h-full">
		<div class="w-full h-full">
			<SvelteFlow
				{nodes}
				{edges}
				{nodeTypes}
				onnodedrag={onNodeDrag}
				onnodedragstop={onNodeDragStop}
				onnodeclick={onNodeClick}
				onedgeclick={onEdgeClick}
				fitView
				proOptions={{ hideAttribution: true }}
				nodesDraggable={true}
				nodesConnectable={false}
				elementsSelectable={true}
			>
				<Background variant="dots" gap={20} size={1} color="rgba(223, 105, 117, 0.1)" />
				<Controls />
			</SvelteFlow>
		</div>
	</main>

	<!-- Node Sidebar -->
	<NodeSidebar bind:isOpen={nodeSidebarOpen} onAddNode={handleAddNode} />
</div>

<style>
	:global(.amq-gradient-text) {
		background: linear-gradient(135deg, #DF6975 0%, #E8899A 50%, #75B9DF 100%);
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
		background-clip: text;
	}
</style>
